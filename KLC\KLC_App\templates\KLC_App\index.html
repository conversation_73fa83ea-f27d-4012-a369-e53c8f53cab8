{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head class="pb-5">
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <meta name="description" content="موقع مجلس قروي كفرعين الإلكتروني" />
    <meta name="author" content="" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>مجلس قروي كفرعين - الصفحة الرئيسية</title>
    <!-- Force light mode by default -->
    <script>
      // Set light mode immediately before any other scripts run
      document.documentElement.setAttribute("data-theme", "light");
      localStorage.setItem("theme", "light");
    </script>
    <!-- Bootstrap 5.3 CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Favicon-->
    <link
      rel="icon"
      type="image/x-icon"
      href="{% static 'images/logo.png' %}"
    />
    <!-- Core theme CSS (includes Bootstrap)-->
    <link href="{% static 'css/index.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Theme CSS -->
    <link href="{% static 'css/theme.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Section CSS -->
    <link href="{% static 'css/news.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- About Section CSS -->
    <link href="{% static 'css/about.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Modern News Layout CSS -->
    <link href="{% static 'css/modern-news.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- News Image Carousel CSS -->
    <link href="{% static 'css/news-image-carousel.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!-- Sections CSS -->
    <link href="{% static 'css/sections.css' %}?v={{ STATIC_VERSION }}" rel="stylesheet" />
    <!--Link the js file-->
    <script src="{% static 'js/index.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <!-- Theme JS -->
    <script src="{% static 'js/theme.js' %}?v={{ STATIC_VERSION }}" defer></script>
    <!--Link the footer js file-->
    <script src="{% static 'js/footer.js' %}?v={{ STATIC_VERSION }}" defer></script>
  </head>
  <body id="page-top">
    <!-- Navigation-->
    <nav
      class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top shadow-sm mb-5"
      id="mainNav"
    >
      <div class="container px-4">
        <a class="logo d-flex align-items-center text-decoration-none" href="#page-top">
          <img
            src="{% static 'images/logo.png' %}"
            alt="logo"
            width="150"
            height="100"
            {% comment %} class="d-inline-block align-text-top rounded-circle shadow-sm border border-light border-opacity-25" {% endcomment %}
          />
          <span class="ms-2 d-none d-lg-inline text-light fw-bold text-shadow">مجلس قروي كفرعين</span>
        </a>
        <button
          class="navbar-toggler border-0 shadow-sm"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarResponsive"
          aria-controls="navbarResponsive"
          aria-expanded="false"
          aria-label="Toggle navigation"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarResponsive">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#about"><i class="fas fa-info-circle me-1"></i> عن الموقع</a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#vision"><i class="fas fa-eye me-1"></i> رؤية المجلس</a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#news_achievements"><i class="fas fa-newspaper me-1"></i> أخبار وإنجازات</a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#about_village"><i class="fas fa-map-marker-alt me-1"></i> عن قرية كفرعين</a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="#contact-us"><i class="fas fa-envelope me-1"></i> تواصل معنا</a>
            </li>
            <li class="nav-item">
              <a class="nav-link px-3 mx-1 rounded-pill" href="{% url 'about_developers' %}"
                ><i class="fas fa-code me-1"></i> عن المطورين</a
              >
            </li>
          </ul>
        </div>
      </div>
    </nav>
    <!-- Header -->
    <header class="text-white text-center h-100">
      <div
        class="container px-4 text-center bg-dark bg-gradient bg-opacity-75 w-100 py-4 mx-auto mb-5 rounded-3 shadow-lg"
        style="max-width: 90%"
      >
        <div class="container text-center" id="header-text">
          <h1
            class="display-5 fw-bolder mb-3 border-bottom border-3 border-light pb-3 rounded-3 text-center w-100 text-shadow"
            style="font-size: 1.8rem"
          >
            <i class="fas fa-landmark me-2"></i>موقع مجلس قروي كفرعين الإلكتروني
          </h1>
          <p class="lead fw-normal" style="font-size: 1rem">
            نسعى لتقديم خدمات إلكترونية متكاملة لتسهيل تواصلكم مع المجلس
          </p>
        </div>
      </div>

      <div
        class="container d-flex justify-content-center align-items-center mx-auto text-white my-7"
      >
        <div class="text-center w-100">
          <form
            action="{% url 'check_person_id' %}"
            method="POST"
            class="bg-white text-dark p-4 rounded-lg shadow-lg w-100 w-md-50 mx-auto border border-secondary border-opacity-25"
            style="max-width: 500px"
            id="person-id-form"
          >
            {% csrf_token %}
            <div class="input-group mb-3 py-2">
              <input
                id="person-id"
                type="text"
                class="form-control form-control-lg border-secondary"
                placeholder="أدخل رقم الهوية"
                aria-label="Person's ID"
                aria-describedby="button-addon1"
                title="أدخل رقم البطاقة الشخصية الخاصة بك لتتمتع بخدماتنا الإلكترونية"
                name="person_id"
                required
                maxlength="9"
                minlength="9"
              />
              <button
                type="submit"
                class="btn btn-danger btn-lg me-2 shadow-sm"
                title="تمتع بخدماتنا الإلكترونية"
                id="start-button"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="36"
                  height="36"
                  fill="currentColor"
                  class="bi bi-box-arrow-in-left"
                  viewBox="0 0 16 16"
                >
                  <path
                    fill-rule="evenodd"
                    d="M10 3.5a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v9a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-2a.5.5 0 0 1 1 0v2A1.5 1.5 0 0 1 9.5 14h-8A1.5 1.5 0 0 1 0 12.5v-9A1.5 1.5 0 0 1 1.5 2h8A1.5 1.5 0 0 1 11 3.5v2a.5.5 0 0 1-1 0z"
                  />
                  <path
                    fill-rule="evenodd"
                    d="M4.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L5.707 7.5H14.5a.5.5 0 0 1 0 1H5.707l2.147 2.146a.5.5 0 0 1-.708.708z"
                  />
                </svg>
              </button>
            </div>
            <div class="text-center mb-3">
              <p class="text-danger fw-medium" style="font-size: 0.9rem">
                <i class="fas fa-info-circle me-1"></i>يرجى إدخال رقم الهوية بشكل صحيح لتتمكن من الاستفادة من خدماتنا
                الإلكترونية.
              </p>
            </div>
          </form>
          <!-- Check the status that is from server side -->
          {% if status %}
          <div class="text-center mt-3">
            {% if status == "error" %}
            <div class="alert alert-danger" role="alert">
              حدث خطأ أثناء معالجة الطلب.
            </div>
            {% elif status == "not numeric" or status == "unauthorized" %}
            <div class="alert alert-warning" role="alert">
              الرجاء إدخال رقم الهوية بشكل صحيح.
            </div>
            {% elif status == "unauthorized_repeat"%}
            <script>
              // Automatically show the unauthorized repeat attempt modal when the page loads
              document.addEventListener("DOMContentLoaded", function () {
                var unauth_repeat_attempt_modal = new bootstrap.Modal(
                  document.getElementById("unauthRepeatAttemptModal")
                );
                unauth_repeat_attempt_modal.show();
              });
            </script>
            {% elif status == "authorized" %}
            <script>
              const delay = 3000; // 3 seconds delay
              const loadingAnimation = document.createElement("div");
              loadingAnimation.className = "loading-animation";
              loadingAnimation.innerHTML = `
                        <div class="loader"></div>
                        <p class="text-center" style="color: white; font-size: 1.5rem; margin-top: 20px;">سيتم تحويلك إلى صفحة خدماتي، إنتظر قليلاً.....</p>
                    `;
              loadingAnimation.style.position = "fixed";
              loadingAnimation.style.top = "50%";
              loadingAnimation.style.left = "50%";
              loadingAnimation.style.transform = "translate(-50%, -50%)";
              loadingAnimation.style.zIndex = "9999";
              loadingAnimation.style.display = "flex";
              loadingAnimation.style.flexDirection = "column";
              loadingAnimation.style.justifyContent = "center";
              loadingAnimation.style.alignItems = "center";
              loadingAnimation.style.background = "rgba(0, 0, 0, 0.8)";
              loadingAnimation.style.width = "100vw";
              loadingAnimation.style.height = "100vh";

              const loader = loadingAnimation.querySelector(".loader");
              loader.style.border = "16px solid #f3f3f3";
              loader.style.borderTop = "16px solid rgb(197, 79, 0)";
              loader.style.borderRadius = "50%";
              loader.style.width = "120px";
              loader.style.height = "120px";
              loader.style.animation = "spin 2s linear infinite";

              const style = document.createElement("style");
              style.innerHTML = `
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    `;
              document.head.appendChild(style);

              document.body.appendChild(loadingAnimation);
              setTimeout(() => {
                document.body.removeChild(loadingAnimation);
                window.location.href = "/services";
              }, delay);
            </script>
            {% endif %}
          </div>
          {% endif %}
        </div>
      </div>
    </header>
    <!-- About Website section-->
    <section id="about" class="about-section">
      <div class="container px-4">
        <div class="section-header text-center mb-4">
          <h2 class="position-relative d-inline-block">
            <i class="fas fa-info-circle"></i> نبذة عن الموقع
          </h2>
          <div class="section-header-line"></div>
        </div>

        <!-- Introduction text -->
        <div class="about-intro-container text-center mb-5">
          <p class="about-intro">
            موقع مجلس قروي كفرعين الإلكتروني هو منصة متكاملة تهدف إلى تسهيل
            تواصل المواطنين مع المجلس وتقديم خدماته بشكل رقمي.
          </p>
        </div>

        <!-- Main content area -->
        <div class="about-main-content">
          <!-- Services overview with icon -->
          <div class="about-overview mb-5">
            <div class="about-overview-icon">
              <i class="fas fa-laptop-code"></i>
            </div>
            <h3 class="about-overview-title">خدمات المنصة الإلكترونية</h3>
          </div>

          <!-- Features grid -->
          <div class="about-features-grid">
            <!-- Feature 1 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-file-alt"></i>
              </div>
              <div class="about-feature-content">
                <h4>طلب المعاملات</h4>
                <p>يوفر الموقع للمواطنين إمكانية طلب المعاملات وإدارتها بشكل إلكتروني.</p>
              </div>
            </div>

            <!-- Feature 2 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-money-bill-wave"></i>
              </div>
              <div class="about-feature-content">
                <h4>استعلامات مالية</h4>
                <p>التحقق من مستحقات النقايات إلكترونياً بطريقة سهلة وسريعة.</p>
              </div>
            </div>

            <!-- Feature 3 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-map-marked-alt"></i>
              </div>
              <div class="about-feature-content">
                <h4>خرائط وأراضي</h4>
                <p>عرض أحواض وأراضي ومساحات كفرعين بشكل تفاعلي.</p>
              </div>
            </div>

            <!-- Feature 4 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-tasks"></i>
              </div>
              <div class="about-feature-content">
                <h4>حجز الصالة</h4>
                <p>يوفر الموقع للمواطنين إمكانية حجز صالة المجلس بشكل إلكتروني.</p>
              </div>
            </div>

            <!-- Feature 5 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-newspaper"></i>
              </div>
              <div class="about-feature-content">
                <h4>أخبار وتحديثات</h4>
                <p>متابعة الأخبار والتحديثات المتعلقة بالخدمات المحلية والمجتمع.</p>
              </div>
            </div>

            <!-- Feature 6 -->
            <div class="about-feature-item">
              <div class="about-feature-icon">
                <i class="fas fa-bullhorn"></i>
              </div>
              <div class="about-feature-content">
                <h4>شكاوي واقتراحات</h4>
                <p>يمكنكم الإبلاغ عن شكاويكم واقتراحاتكم ومشاكلكم المتعلقة بالمجلس بشكل إلكتروني.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- Council Vision section-->
    <section class="bg-light" id="vision">
      <div class="container px-4 py-5">
        <div class="row gx-4 justify-content-center">
          <div class="col-lg-10">
            <!-- Section Header -->
            <div class="text-center mb-5">
              <h2 class="display-5 fw-bold text-dark mb-3">
                <i class="fas fa-eye me-2"></i>رؤية المجلس
              </h2>
              <div class="mx-auto" style="width: 100px; height: 4px; background: linear-gradient(90deg, #dc3545, #ffc107); border-radius: 2px;"></div>
            </div>

            <!-- Main Vision Title -->
            <div class="text-center mb-5">
              <h3 class="h2 fw-bold text-dark mb-4" style="line-height: 1.6;">
                الرؤية الشمولية لخطة التنمية لقرية كفرعين (2022)
              </h3>

              <!-- Vision Statement -->
              <div class="card border-0 shadow-lg mb-4" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                <div class="card-body p-4">
                  <p class="lead text-dark mb-3" style="font-size: 1.1rem; line-height: 1.8;">
                    تقوم الرؤية الشمولية لخطة التنمية لقرية كفرعين على بناء مجتمع قروي متكامل ومتطور، من خلال تفعيل المشاركة المجتمعية وتحديد أولويات واقعية تلبي احتياجات السكان وتعزز من جودة حياتهم، وذلك تحت شعار:
                  </p>
                  <div class="alert border-0 shadow-sm" style="background: linear-gradient(90deg, #dc3545, #c82333);">
                    <h4 class="text-white text-center fw-bold mb-0" style="font-size: 1.3rem;">
                      <i class="fas fa-quote-right me-2"></i>
                      "كفرعين بلدة نموذجية، آمنة، تواكب التطور، تجذب السياحة والاستثمار، وتشكل مركزًا متميزًا للوسط المحيط"
                      <i class="fas fa-quote-left ms-2"></i>
                    </h4>
                  </div>
                </div>
              </div>
            </div>

            <!-- Vision Foundations -->
            <div class="row mb-5">
              <div class="col-12">
                <div class="card border-0 shadow-sm h-100">
                  <div class="card-header text-white text-center py-3" style="background: linear-gradient(90deg, #343a40, #495057);">
                    <h4 class="mb-0 fw-bold">
                      <i class="fas fa-foundation me-2"></i>الأسس التي تقوم عليها الرؤية
                    </h4>
                  </div>
                  <div class="card-body p-4">
                    <div class="row">
                      <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-start">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(90deg, #dc3545, #c82333);">
                              <i class="fas fa-users"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0" style="line-height: 1.6;">&nbsp;العمل الجماعي والمشاركة المجتمعية كأساس في التخطيط والتنفيذ، بمشاركة فعالة من جميع شرائح المجتمع المحلي.</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-start">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(90deg, #ffc107, #e0a800);">
                              <i class="fas fa-search"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0" style="line-height: 1.6;">تشخيص شامل للواقع التنموي في ثلاث مجالات رئيسية: البنية التحتية والبيئة والحوكمة، والخدمات الاجتماعية، والاقتصاد والاستثمار.</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-start">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(90deg, #343a40, #495057);">
                              <i class="fas fa-shield-alt"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0" style="line-height: 1.6;">&nbsp; معالجة التحديات الرئيسية التي تواجه سكان القرية، وبخاصة الفئات المهمشة مثل النساء، الشباب، وكبار السن.</p>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-start">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background: linear-gradient(90deg, #6c757d, #5a6268);">
                              <i class="fas fa-leaf"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0" style="line-height: 1.6;">تعزيز استغلال الموارد المحلية (المياه، الأراضي، المواقع السياحية، والمواهب البشرية).</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Main Pillars -->
            <div class="row mb-5">
              <div class="col-12">
                <div class="text-center mb-4">
                  <h3 class="h2 fw-bold text-dark">
                    <i class="fas fa-columns me-2"></i>المحاور الرئيسية للرؤية
                  </h3>
                </div>

                <!-- Pillar 1: Infrastructure -->
                <div class="card border-0 shadow-sm mb-4">
                  <div class="card-header text-white text-center py-3" style="background: linear-gradient(90deg, #dc3545, #c82333);">
                    <h4 class="mb-0 fw-bold">
                      <i class="fas fa-building me-2"></i>1. البنية التحتية والحكم الرشيد
                    </h4>
                  </div>
                  <div class="card-body p-4">
                    <div class="row">
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-road me-3" style="color: #dc3545;"></i>
                          <span>&nbsp; إعادة تأهيل شبكة الطرق الداخلية والرئيسية</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-bolt me-3" style="color: #ffc107;"></i>
                          <span>&nbsp; تحسين خدمات الكهرباء والمياه وتصريف الأمطار</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-water me-3" style="color: #6c757d;"></i>
                          <span>&nbsp; معالجة مشكلة غياب شبكة الصرف الصحي</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-cogs me-3" style="color: #343a40;"></i>
                          <span>&nbsp; تطوير أنظمة إدارة الموارد العامة (الأراضي، النفايات، المباني)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Pillar 2: Social Services -->
                <div class="card border-0 shadow-sm mb-4">
                  <div class="card-header text-white text-center py-3" style="background: linear-gradient(90deg, #ffc107, #e0a800);">
                    <h4 class="mb-0 fw-bold">
                      <i class="fas fa-heart me-2"></i>2. الخدمات الاجتماعية
                    </h4>
                  </div>
                  <div class="card-body p-4">
                    <div class="row">
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-school me-2" style="color: #dc3545;"></i>
                          <span>&nbsp; زيادة عدد الأبنية المدرسية وتحسين البنية التحتية التعليمية</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-hospital me-2" style="color: #ffc107;"></i>
                          <span>&nbsp; تطوير المركز الصحي وتوسيع خدماته لتغطية احتياجات السكان</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-female me-2" style="color: #6c757d;"></i>
                          <span>&nbsp; تمكين المرأة من خلال الجمعيات النسوية</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-running me-2" style="color: #343a40;"></i>
                          <span>&nbsp; دعم الشباب عبر توفير البنية التحتية الرياضية والثقافية</span>
                        </div>
                      </div>
                      <div class="col-12 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-tree me-2" style="color: #dc3545;"></i>
                          <span>&nbsp; إنشاء مرافق عامة وحدائق ومساحات اجتماعية</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Pillar 3: Economy and Investment -->
                <div class="card border-0 shadow-sm mb-4">
                  <div class="card-header text-white text-center py-3" style="background: linear-gradient(90deg, #343a40, #495057);">
                    <h4 class="mb-0 fw-bold">
                      <i class="fas fa-chart-line me-2"></i>3. الاقتصاد والاستثمار
                    </h4>
                  </div>
                  <div class="card-body p-4">
                    <div class="row">
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-seedling me-2" style="color: #dc3545;"></i>
                          <span>&nbsp; دعم الزراعة والثروة الحيوانية عبر توفير البنية التحتية والإرشاد والدعم الفني</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-lightbulb me-2" style="color: #ffc107;"></i>
                          <span>&nbsp; تشجيع المشاريع الصغيرة والمبادرات الشبابية</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-map-marked-alt me-2" style="color: #6c757d;"></i>
                          <span>&nbsp; جذب الاستثمار وتطوير الأنشطة السياحية والبيئية</span>
                        </div>
                      </div>
                      <div class="col-md-6 mb-2">
                        <div class="d-flex align-items-center">
                          <i class="fas fa-handshake me-2" style="color: #343a40;"></i>
                          <span>&nbsp; تعزيز دور القطاع الخاص وإنشاء جمعيات إنتاجية تعاونية</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Development Goals -->
            <div class="row mb-5">
              <div class="col-12">
                <div class="text-center mb-4">
                  <h3 class="h2 fw-bold text-dark">
                    <i class="fas fa-bullseye me-2"></i>الأهداف التنموية المترجمة من الرؤية
                  </h3>
                </div>

                <div class="row">
                  <div class="col-md-6 mb-3">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #dc3545, #c82333);">
                              <i class="fas fa-road"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">تطوير شبكة الطرق والمواصلات</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #ffc107 !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #ffc107, #e0a800);">
                              <i class="fas fa-hospital"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">إنشاء مركز صحي نموذجي</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #343a40 !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #343a40, #495057);">
                              <i class="fas fa-school"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">تحسين جودة التعليم وبناء مدارس جديدة</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #6c757d !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #6c757d, #5a6268);">
                              <i class="fas fa-seedling"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">دعم القطاع الزراعي واستصلاح الأراضي</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #dc3545 !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #dc3545, #c82333);">
                              <i class="fas fa-running"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">تمكين الشباب من خلال مبادرات رياضية وثقافية</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #ffc107 !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #ffc107, #e0a800);">
                              <i class="fas fa-map-marked-alt"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">تطوير المرافق السياحية والتراثية</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="col-12 mb-3">
                    <div class="card border-0 shadow-sm h-100" style="border-left: 4px solid #343a40 !important;">
                      <div class="card-body p-3">
                        <div class="d-flex align-items-center">
                          <div class="flex-shrink-0">
                            <div class="text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 35px; height: 35px; background: linear-gradient(90deg, #343a40, #495057);">
                              <i class="fas fa-landmark"></i>
                            </div>
                          </div>
                          <div class="flex-grow-1 ms-3">
                            <p class="mb-0 fw-medium">دعم البنية التحتية للمجلس القروي وتعزيز دوره التنموي</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Conclusion -->
            <div class="row mt-5">
              <div class="col-12">
                <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #dc3545 0%, #343a40 100%);">
                  <div class="card-body p-4 text-center">
                    <h4 class="text-white fw-bold mb-3">
                      <i class="fas fa-heart me-2"></i>رسالة الرؤية
                    </h4>
                    <p class="text-white mb-0" style="font-size: 1.1rem; line-height: 1.8;">
                      هذه الرؤية تسعى إلى تحقيق تنمية متكاملة ومستدامة لقرية كفرعين عبر تعاون جميع الأطراف المحلية والرسمية، بما يضمن تحسين مستوى المعيشة والحفاظ على الموارد وتعزيز الانتماء المجتمعي.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- News and Achievements section -->
    <section id="news_achievements" class="news-section">
      <div class="container-fluid px-4">
        <div class="row gx-4 justify-content-center">
          <div class="col-12">
            <h2 class="text-center mb-3 news-section-title" style="font-weight: bold; position: relative; padding-bottom: 15px;">
              <i class="fas fa-newspaper"></i> أخبار وإنجازات المجلس
              <span style="display: block; width: 80px; height: 3px;"></span>
            </h2>
            <p class="lead text-center fw-bold mb-4 news-section-subtitle" style="font-size: 1.2rem; border-bottom: 2px solid #dc3545; padding-bottom: 10px; max-width: 800px; margin-left: auto; margin-right: auto;">
              متابعة آخر أخبار ومشاريع وإنجازات المجلس القروي الرسمية
            </p>

            <!-- Professional News Grid Layout -->
            <div class="news-grid-container mt-4">
              {% if news_group %}
              <!-- Featured news and latest news -->
              <div class="news-showcase">
                <!-- First row with featured news -->
                <div class="row mb-4">
                  <!-- Featured large news item -->
                  {% if featured_news %}
                  <div class="col-lg-6 mb-4 featured-news-col">
                    <div class="featured-news-card news-card">
                      <div class="featured-img-container">
                        <!-- Always use the same structure for both single and multiple images -->
                        {% if featured_news.additional_images %}
                          <!-- Image carousel for news with multiple images -->
                          <div class="news-image-carousel featured-news-carousel">
                            <!-- Main image slide -->
                            <div class="carousel-slide active">
                              <img
                                src="{{ featured_news.image_src }}"
                                alt="{{ featured_news.title }}"
                                class="img-fluid"
                                onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ featured_news.image_src }}');"
                              />
                            </div>

                            <!-- Additional image slides -->
                            {% for img_src in featured_news.additional_images %}
                              <div class="carousel-slide">
                                <img
                                  src="{{ img_src }}"
                                  alt="{{ featured_news.title }} - صورة {{ forloop.counter|add:1 }}"
                                  class="img-fluid"
                                  onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ img_src }}');"
                                />
                              </div>
                            {% endfor %}

                            <!-- Carousel indicators - only dots -->
                            <div class="carousel-indicators featured-carousel-indicators">
                              <!-- Will be populated by JavaScript -->
                            </div>
                          </div>
                        {% else %}
                          <!-- Single image display with the same structure -->
                          <img
                            src="{{ featured_news.image_src }}"
                            alt="{{ featured_news.title }}"
                            class="img-fluid"
                            onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ featured_news.image_src }}');"
                          />
                        {% endif %}

                        <!-- Featured news overlay - always displayed regardless of image count -->
                        <div class="featured-news-overlay">
                          <span class="featured-news-category">
                            <i class="fas fa-tag me-1"></i>{{ featured_news.type|default:"أخبار" }}
                          </span>
                          <div class="featured-news-date">
                            <i class="fas fa-calendar-alt me-1"></i>{{ featured_news.published_at }}
                          </div>
                          <h3 class="featured-news-title">
                            {{ featured_news.title }}
                          </h3>
                          <p class="featured-news-excerpt">
                            {{ featured_news.Description|truncatechars:300 }}
                          </p>
                          <div class="featured-news-buttons">
                            <a
                              href="{% url 'news_detail' featured_news.id %}"
                              class="btn btn-light"
                              title="اقرأ المزيد عن {{ featured_news.title }}"
                            >
                              <i class="fas fa-book-open me-1"></i> اقرأ
                              المزيد
                            </a>
                            {% if featured_news.video_url %}
                            <button
                              type="button"
                              class="btn btn-danger ms-2"
                              data-bs-toggle="modal"
                              data-bs-target="#videoModal"
                              data-video-src="{{ featured_news.video_url }}"
                              data-news-title="{{ featured_news.title }}"
                              data-news-desc="{{ featured_news.Description }}"
                              data-news-date="{{ featured_news.published_at }}"
                              title="شاهد فيديو {{ featured_news.title }}"
                            >
                              <i class="fas fa-play-circle me-1"></i>
                              الفيديو
                            </button>
                            {% endif %}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {% endif %}

                  <!-- Right column with two medium news items -->
                  <div class="{% if featured_news %}col-lg-6{% else %}col-lg-12{% endif %}">
                    <div class="row">
                      {% for news in latest_news|slice:":2" %}
                      <div class="col-md-6 mb-4">
                        <div class="medium-news-card news-card">
                          <div class="medium-img-container">
                            {% if news.additional_images %}
                              <!-- Image carousel for news with multiple images -->
                              <div class="news-image-carousel">
                                <!-- Main image slide -->
                                <div class="carousel-slide active">
                                  <img
                                    src="{{ news.image_src }}"
                                    alt="{{ news.title }}"
                                    class="img-fluid"
                                    onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');"
                                  />
                                </div>

                                <!-- Additional image slides -->
                                {% for img_src in news.additional_images %}
                                  <div class="carousel-slide">
                                    <img
                                      src="{{ img_src }}"
                                      alt="{{ news.title }} - صورة {{ forloop.counter|add:1 }}"
                                      class="img-fluid"
                                      onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ img_src }}');"
                                    />
                                  </div>
                                {% endfor %}

                                <!-- Carousel indicators -->
                                <div class="carousel-indicators">
                                  <!-- Will be populated by JavaScript -->
                                </div>
                              </div>
                            {% else %}
                              <!-- Single image display -->
                              <img
                                src="{{ news.image_src }}"
                                alt="{{ news.title }}"
                                class="img-fluid"
                                onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');"
                              />
                            {% endif %}
                            <span class="news-category-tag"
                              >{{ news.type|default:"أخبار" }}</span
                            >
                            {% if news.additional_images %}
                              <div class="">
                                <i class="fas fa-images"></i> {{ news.additional_images|length|add:1 }}
                              </div>
                            {% endif %}
                          </div>
                          <div class="medium-news-content">
                            <div class="news-date">
                              <i class="fas fa-calendar-alt me-1"></i>{{ news.published_at }}
                            </div>
                            <h5 class="medium-news-title">
                              {{ news.title }}
                            </h5>
                            <div class="medium-news-buttons">
                              <a
                                href="{% url 'news_detail' news.id %}"
                                class="btn btn-outline-danger"
                                title="اقرأ المزيد عن {{ news.title }}"
                              >
                                <i class="fas fa-book-open me-1"></i> اقرأ
                                المزيد
                              </a>
                              {% if news.video_url %}
                              <button
                                type="button"
                                class="btn btn-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#videoModal"
                                data-video-src="{{ news.video_url }}"
                                data-news-title="{{ news.title }}"
                                data-news-desc="{{ news.Description }}"
                                data-news-date="{{ news.published_at }}"
                                title="شاهد فيديو {{ news.title }}"
                              >
                                <i class="fas fa-play-circle me-1"></i>
                              </button>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                      {% endfor %}
                    </div>
                  </div>
                </div>

                <!-- Second row with smaller news items -->
                <div class="row">
                  {% for news in latest_news|slice:"2:4" %}
                  <div class="col-lg-6 col-md-6 mb-4">
                    <div class="small-news-card news-card">
                      <div class="row g-0">
                        <div class="col-md-5">
                          <div class="small-img-container">
                            {% if news.additional_images %}
                              <!-- Image carousel for news with multiple images -->
                              <div class="news-image-carousel">
                                <!-- Main image slide -->
                                <div class="carousel-slide active">
                                  <img
                                    src="{{ news.image_src }}"
                                    alt="{{ news.title }}"
                                    class="img-fluid h-100"
                                    onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');"
                                  />
                                </div>

                                <!-- Additional image slides -->
                                {% for img_src in news.additional_images %}
                                  <div class="carousel-slide">
                                    <img
                                      src="{{ img_src }}"
                                      alt="{{ news.title }} - صورة {{ forloop.counter|add:1 }}"
                                      class="img-fluid h-100"
                                      onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ img_src }}');"
                                    />
                                  </div>
                                {% endfor %}

                                <!-- Carousel indicators -->
                                <div class="carousel-indicators">
                                  <!-- Will be populated by JavaScript -->
                                </div>
                              </div>
                            {% else %}
                              <!-- Single image display -->
                              <img
                                src="{{ news.image_src }}"
                                alt="{{ news.title }}"
                                class="img-fluid h-100"
                                onerror="this.onerror=null; this.src='{% static 'images/image-not-found.jpg' %}'; console.error('Failed to load image: {{ news.image_src }}');"
                              />
                            {% endif %}
                            <span class="small-news-category"
                              >{{ news.type|default:"أخبار" }}</span
                            >
                            {% if news.additional_images %}
                              <div class="">
                                <i class="fas fa-images"></i> {{ news.additional_images|length|add:1 }}
                              </div>
                            {% endif %}
                          </div>
                        </div>
                        <div class="col-md-7">
                          <div class="small-news-content">
                            <div class="small-news-date">
                              <i class="fas fa-calendar-alt me-1"></i>{{ news.published_at }}
                            </div>
                            <h6 class="small-news-title">
                              {{ news.title }}
                            </h6>
                            <p class="small-news-excerpt">
                              {{ news.Description|truncatechars:80 }}
                            </p>
                            <div class="small-news-buttons">
                              <a
                                href="{% url 'news_detail' news.id %}"
                                class="btn btn-outline-danger"
                                title="اقرأ المزيد عن {{ news.title }}"
                              >
                                <i class="fas fa-book-open me-1"></i> اقرأ
                                المزيد
                              </a>
                              {% if news.video_url %}
                              <button
                                type="button"
                                class="btn btn-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#videoModal"
                                data-video-src="{{ news.video_url }}"
                                data-news-title="{{ news.title }}"
                                data-news-desc="{{ news.Description }}"
                                data-news-date="{{ news.published_at }}"
                                title="شاهد فيديو {{ news.title }}"
                              >
                                <i class="fas fa-play-circle"></i>
                              </button>
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  {% endfor %}
                </div>

                <!-- View All News Button -->
                <div class="text-center mt-4">
                  <a href="{% url 'news_list' %}" class="btn btn-danger btn-lg">
                    <i class="fas fa-newspaper me-2"></i> عرض جميع الأخبار ({{ total_news_count }})
                  </a>
                </div>
              </div>
              {% else %}
              <div class="alert alert-info">لا توجد أخبار متاحة حالياً</div>
              {% endif %}
            </div>

            <!-- Get video modal -->
            {% include 'KLC_App/video_news_modal.html' %}
            <!-- Get news detail modal -->
            {% include 'KLC_App/news_detail_modal.html' %}
          </div>
        </div>
      </div>
    </section>
    <!-- About Village section-->
    <section class="bg-light" id="about_village">
      <div class="container px-4">
        <div class="row gx-4 justify-content-center">
          <div class="col-lg-8">
            <h2>
              <i class="fa-sharp-duotone fa-solid fa-magnifying-glass"></i> عن
              قرية كفرعين
            </h2>
            <p class="lead">قريباً..</p>
          </div>
        </div>
      </div>
    </section>
    <!-- Footer-->
    <div id="contact-us">{% include 'KLC_App/footer.html' %}</div>
    <!-- Theme Toggle Button -->
    <button class="theme-toggle" aria-label="Toggle theme">
      <i class="fas fa-moon"></i>
    </button>
    <!--Get unauthorized repeat attempt modal -->
    {% include 'KLC_App/unauth_repeat_attempt_modal.html' %}
    <!-- Bootstrap 5.3 JS and Popper.js (needed for some components, needed for modals)-->
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"></script>
    <script
      src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.min.js"
      defer
    ></script>
    <!-- Font Awesome CDN -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- News Pagination Script -->
    <script src="{% static 'js/news_pagination.js' %}"></script>
    <!-- News Image Carousel Script -->
    <script src="{% static 'js/news-image-carousel.js' %}?v={{ STATIC_VERSION }}"></script>
  </body>
</html>
